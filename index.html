<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta content="width=device-width, initial-scale=1.0" name="viewport" />
    <title>CVHAT</title>
    <link
      href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css"
      rel="stylesheet"
    />
    <link
      href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;700&amp;display=swap"
      rel="stylesheet"
    />
    <link
      href="https://fonts.googleapis.com/icon?family=Material+Icons"
      rel="stylesheet"
    />
    <style>
      body {
        font-family: "Montserrat", sans-serif;
        background-color: #f7fafc;
      }
    </style>
  </head>
  <body class="flex bg-gray-100">
    <!-- Mobile burger menu button -->
    <button
      id="mobileMenuBtn"
      class="fixed top-4 left-4 z-50 md:hidden bg-blue-900 text-white p-2 rounded-lg shadow-lg"
    >
      <span class="material-icons">menu</span>
    </button>

    <!-- Mobile overlay -->
    <div
      id="mobileOverlay"
      class="fixed inset-0 bg-black bg-opacity-50 z-40 hidden md:hidden"
    ></div>

    <aside
      id="sidebar"
      class="w-64 bg-white h-screen flex flex-col justify-between p-6 shadow-lg fixed md:relative z-50 transform -translate-x-full md:translate-x-0 transition-transform duration-300 ease-in-out"
    >
      <div>
        <div class="flex items-center mb-10">
          <div
            class="w-10 h-10 bg-blue-900 rounded-full flex items-center justify-center text-white font-bold text-xl"
          >
            M
          </div>
          <h1 class="text-2xl font-bold ml-4 text-blue-900">CVHAT</h1>
        </div>
        <div class="grid grid-cols-1 gap-6">
          <div class="bg-gray-200 p-6 rounded-2xl text-center shadow-md">
            <h2 class="text-lg font-semibold text-gray-700">AI Reviews</h2>
            <p class="text-4xl font-bold text-gray-800 mt-2">7</p>
          </div>
          <div class="bg-gray-200 p-6 rounded-2xl text-center shadow-md">
            <h2 class="text-lg font-semibold text-gray-700">
              Recruiter Feedback
            </h2>
            <p class="text-4xl font-bold text-gray-800 mt-2">15</p>
          </div>
        </div>
      </div>
      <button
        class="w-full flex items-center justify-center bg-blue-500 hover:bg-blue-600 text-white font-bold py-4 px-4 rounded-xl text-lg shadow-md"
      >
        <span class="material-icons mr-2">add</span>
        New
      </button>
    </aside>
    <main class="flex-1 p-10 md:ml-0">
      <header class="flex justify-between items-center mb-8">
        <div>
          <h2 class="text-3xl font-bold text-gray-800">Recents</h2>
        </div>
        <div class="flex items-center">
          <a
            class="text-lg text-blue-500 font-semibold hover:underline"
            href="#"
            >View All &gt;</a
          >
          <span
            class="material-icons text-4xl text-gray-600 ml-8 cursor-pointer"
            >settings</span
          >
        </div>
      </header>
      <div
        class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8"
      >
        <div class="bg-blue-900 text-white p-4 rounded-2xl shadow-lg">
          <div class="flex items-center">
            <img
              alt="Document thumbnail"
              class="w-20 h-24 object-cover rounded-md"
              src="https://lh3.googleusercontent.com/aida-public/AB6AXuDH1Y52Dgd4J8XWJvp7VD7pcBxFtHLhDGKLDvDzojBTeeJ9SKnorghjrTEBftJ4uUBf9Ms0x0Nt6mh_zqeg8gRL0pS_LHxWKvrzeJJl0cWfM9ESCJxXJ2viJ4Xk75ci11KbD8HogaRSndHHvEWTuOYatI4a2oAHBVS9SS2eU2oOdtIPe8Yp0s7IPaHmyO-diLgknb9KNkIzpVILtnNopyJ263zN-psz_MdN6ygMmD2-jZmVGAhJ-ZO_J88xZ_vP3CBblgg0JitkY2qy"
            />
            <div class="ml-4 flex-grow">
              <h3 class="font-bold text-lg">Title</h3>
              <p class="text-sm text-gray-300">File name.pdf</p>
            </div>
            <div
              class="bg-white text-blue-900 rounded-full w-8 h-8 flex items-center justify-center font-bold text-xs"
            >
              AI
            </div>
          </div>
          <p class="text-sm text-right text-gray-300 mt-4">14 Aug 2025</p>
        </div>
        <div class="bg-blue-900 text-white p-4 rounded-2xl shadow-lg">
          <div class="flex items-center">
            <img
              alt="Document thumbnail"
              class="w-20 h-24 object-cover rounded-md"
              src="https://lh3.googleusercontent.com/aida-public/AB6AXuCfWXPwDWXWiocwteeN-snJ4zVRogLDPFKnvbHKQXnRFqBkyXAxGsNV2ApCib1pDZaXpMfaPgFKnXfM5rZzlvZO3Im3WDnsOU4U9o6KEXvbWo-gFyKbbCEXuqfwe_rNFWudHKwvC_lyQ65J6buPKSiKh83OHDGrfZiLSA7C91QEjEVnhG4TWW2j7nToSVLlrVtcbnxExc128SZ_Fmvc46Epk7VzmsHLvthSzwcay3hCyRRaej8Zi62EEce1XppqAgXF-Hbb10MXDIJI"
            />
            <div class="ml-4 flex-grow">
              <h3 class="font-bold text-lg">Title</h3>
              <p class="text-sm text-gray-300">File name.pdf</p>
            </div>
            <div
              class="bg-white text-blue-900 rounded-full w-8 h-8 flex items-center justify-center font-bold text-xs"
            >
              AI
            </div>
          </div>
          <p class="text-sm text-right text-gray-300 mt-4">14 Aug 2025</p>
        </div>
        <div class="bg-blue-900 text-white p-4 rounded-2xl shadow-lg">
          <div class="flex items-center">
            <img
              alt="Document thumbnail"
              class="w-20 h-24 object-cover rounded-md"
              src="https://lh3.googleusercontent.com/aida-public/AB6AXuAvvO3O9FANSW-zVom_H-224A3SuXxj5E_Z2FFZ2hNGArq8y8XRFaRhVv3dkd3Iu9BCaapaW1CMG6LQc7Rm51rDX2vsEHepT--isS25LUyZxvQ3dcTvoMG50mKQ7f9nHCCCVYY1kIchjCZqwK-QHSHFd9QCICgb5h_8QwjdzuBUHGYZxENgW509P5qygVdrQ4l-x--HxUqzujqfvCLw8l4aiIoU2bZjeuCyqk3kP1FeCZ8EdKj4lIvk72-cIGfFd3C_p7w-hqSeXyjh"
            />
            <div class="ml-4 flex-grow">
              <h3 class="font-bold text-lg">Title</h3>
              <p class="text-sm text-gray-300">File name.pdf</p>
            </div>
            <div
              class="bg-white text-blue-900 rounded-full w-8 h-8 flex items-center justify-center font-bold text-xs"
            >
              AI
            </div>
          </div>
          <p class="text-sm text-right text-gray-300 mt-4">14 Aug 2025</p>
        </div>
        <div class="bg-blue-900 text-white p-4 rounded-2xl shadow-lg">
          <div class="flex items-center">
            <img
              alt="Document thumbnail"
              class="w-20 h-24 object-cover rounded-md"
              src="https://lh3.googleusercontent.com/aida-public/AB6AXuBCd4lK8CWxdpr0G7U0ETKcoMpVAKNWVWH6dvlHrhRupZwMEaYaYcLkyVgRvEu60Ywpp1Z4rcUKPn8Jyvrdi5ww5BtEhvb1UR96QQeWiKHVHyde2X1ele1wT68q0tI6N1YSdt1d8agjYGmA7_awEZyNsaYuh52dHMXxWsW7LkEO7ybquYUM2ZaUBY1n6kX2gZbBFbbJcEeSbMrZhHtij3aoSNXc0SLp_2m14YMU2o8wX1uoD792f72IBsORnhdOKxo8DHETHUT3WFaW"
            />
            <div class="ml-4 flex-grow">
              <h3 class="font-bold text-lg">Title</h3>
              <p class="text-sm text-gray-300">File name.pdf</p>
            </div>
            <div
              class="bg-white text-blue-900 rounded-full w-8 h-8 flex items-center justify-center font-bold text-xs"
            >
              AI
            </div>
          </div>
          <p class="text-sm text-right text-gray-300 mt-4">14 Aug 2025</p>
        </div>
        <div class="bg-blue-900 text-white p-4 rounded-2xl shadow-lg">
          <div class="flex items-center">
            <img
              alt="Document thumbnail"
              class="w-20 h-24 object-cover rounded-md"
              src="https://lh3.googleusercontent.com/aida-public/AB6AXuDiU4dB9WyGcO7RNdEym_yil4JnPqWvkK-Rh7JYxIj4zgIoxVzTiCLeMH2pKQFYKWymDxjdZl52h9lJzPQJv2JNCH6dBNq0rGApeiJUtk19SZKt-vvqHeYbhCP7Wsu6BA1bKaSr7JOJWOAr-u1x2qVi-Ir7PbRMKYyuuLP03sBOByCYswsXwBX0dEnXscEuWtAR_eonSzM497RN9TEv5SMvRF_8VXgEMXeQPuUe_ZcsUY6qgA1nCiiJTpliZm8hroWIQiyt3u4vCEVD"
            />
            <div class="ml-4 flex-grow">
              <h3 class="font-bold text-lg">Title</h3>
              <p class="text-sm text-gray-300">File name.pdf</p>
            </div>
            <div
              class="bg-white text-blue-900 rounded-full w-8 h-8 flex items-center justify-center font-bold text-xs"
            >
              AI
            </div>
          </div>
          <p class="text-sm text-right text-gray-300 mt-4">14 Aug 2025</p>
        </div>
      </div>
    </main>

    <script>
      // Mobile menu functionality
      const mobileMenuBtn = document.getElementById("mobileMenuBtn");
      const sidebar = document.getElementById("sidebar");
      const mobileOverlay = document.getElementById("mobileOverlay");

      function toggleMobileMenu() {
        const isOpen = !sidebar.classList.contains("-translate-x-full");

        if (isOpen) {
          // Close menu
          sidebar.classList.add("-translate-x-full");
          mobileOverlay.classList.add("hidden");
        } else {
          // Open menu
          sidebar.classList.remove("-translate-x-full");
          mobileOverlay.classList.remove("hidden");
        }
      }

      // Event listeners
      mobileMenuBtn.addEventListener("click", toggleMobileMenu);
      mobileOverlay.addEventListener("click", toggleMobileMenu);

      // Close menu when clicking outside on mobile
      document.addEventListener("click", function (event) {
        if (window.innerWidth < 768) {
          // md breakpoint
          const isClickInsideSidebar = sidebar.contains(event.target);
          const isClickOnMenuBtn = mobileMenuBtn.contains(event.target);
          const isMenuOpen = !sidebar.classList.contains("-translate-x-full");

          if (!isClickInsideSidebar && !isClickOnMenuBtn && isMenuOpen) {
            toggleMobileMenu();
          }
        }
      });

      // Handle window resize
      window.addEventListener("resize", function () {
        if (window.innerWidth >= 768) {
          // md breakpoint
          // Reset mobile menu state on desktop
          sidebar.classList.remove("-translate-x-full");
          mobileOverlay.classList.add("hidden");
        } else {
          // Ensure menu is closed on mobile
          sidebar.classList.add("-translate-x-full");
          mobileOverlay.classList.add("hidden");
        }
      });
    </script>
  </body>
</html>
